{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-select": "^2.2.5", "@tailwindcss/vite": "^4.1.11", "better-auth": "^1.2.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jotai": "^2.12.5", "lucide-react": "^0.525.0", "path": "^0.12.7", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-toastify": "^11.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "tw-animate-css": "^1.3.6", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}